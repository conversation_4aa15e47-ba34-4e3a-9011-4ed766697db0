import getCategory from "@/actions/category/get-category";
import getProduct from "@/actions/product/get-product";
import getSubCategory from "@/actions/subcategory/get-subcategory";
import NavbarClient from "./navbar";
import { IProduct, ISubCategory } from "@/types/category";


export interface NavItem {
    id: string;
    name: string;
    href: string
}
export interface NavCategory {
    id: string;
    category: string;
    items: NavItem[]
}
export type NavLink = {
    id?: string;
    label: string;
    href: string;
    subItems?: NavItem[];
    subCategories?: NavCategory[];
};

export default async function NavbarLoader() {
    const [catRes, subRes, prodRes] = await Promise.all([
        getCategory(),
        getSubCategory(),
        getProduct(),
    ]);

    const categories = catRes?.data ?? [];
    const subcategories = subRes?.data ?? [];
    const products = prodRes?.data ?? [];

    console.log('categories', categories);
    console.log('subcategories', subcategories);
    console.log('products', products);


    const subsByCategory: Record<string, ISubCategory[]> = {};
    for (const s of subcategories) {
        if (!s?.categoryId) continue;
        (subsByCategory[s.categoryId] ??= []).push(s);
    }
    const prodsBySub: Record<string, IProduct[]> = {};
    for (const p of products) {
        if (!p?.subCategoryId) continue;
        (prodsBySub[p.subCategoryId] ??= []).push(p);
    }

    const dynamicCategoryLinks: NavLink[] = categories.map((cat) => {
        const subs: ISubCategory[] = subsByCategory[cat.id] ?? [];

        const subCategories: NavCategory[] = subs
            .map((sub): NavCategory | null => {
                const prods: IProduct[] = (prodsBySub[sub.id] ?? []).filter((p) => p?.name && p?.slug);
                const items: NavItem[] = prods.map((p) => ({
                    id: p.id,
                    name: p.name,
                    href: `/${cat.slug}/${p.slug}`,
                }));
                if (items.length === 0) return null;
                return {
                    id: sub.id,
                    category: sub.name,
                    items
                };
            })
            .filter(Boolean) as NavCategory[];

        return {
            id: cat.id,
            label: String(cat.name || "").toUpperCase(),
            href: `/${cat.slug}`,
            subCategories: subCategories.length ? subCategories : undefined,
        };
    });

    const navLinks: NavLink[] = [
        { label: "HOME", href: "/" },
        ...dynamicCategoryLinks,
        { label: "ABOUT US", href: "/about" },
        { label: "CERTIFIED", href: "/certified" },
        { label: "CONTACT US", href: "/contact" },
    ];

    return <NavbarClient navLinks={navLinks} />;
}
