"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { ChevronDown, X, Menu, Search } from "lucide-react";
import { Input } from "../ui/input";
import { NavCategory, NavItem, NavLink } from "./navbar-loader";

function Dropdown({
  items, title, isCategory = false, onStayOpen, onClose,
}: {
  items: NavItem[] | NavCategory[];
  title: string;
  isCategory?: boolean;
  onStayOpen: () => void;
  onClose: () => void;
}) {
  return (
    <div
      className="absolute top-full left-0 bg-secondary shadow-lg z-50 w-[500px] p-6 border-t-2 border-primary"
      onMouseEnter={onStayOpen}
      onMouseLeave={onClose}
    >
      <h3 className="text-2xl font-semibold mb-6 text-white border-l-4 border-primary pl-3">{title}</h3>

      {isCategory ? (
        <div className="grid grid-cols-2 gap-6 text-white">
          {(items as NavCategory[]).map((category) => (
            <div key={category.id}>
              <h4 className="text-lg font-semibold mb-3 flex items-center">
                <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
                {category.category}
              </h4>
              <ul className="space-y-2 text-sm">
                {category.items.map((item) => (
                  <li key={item.id}>
                    <Link
                      href={item.href}
                      className="hover:text-primary cursor-pointer transition-colors block"
                      onClick={onClose}
                    >
                      → {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      ) : (
        <ul className="space-y-3 text-white text-sm">
          {(items as NavItem[]).map((item) => (
            <li key={item.id}>
              <Link
                href={item.href}
                className="hover:text-primary cursor-pointer transition-colors block"
                onClick={onClose}
              >
                → {item.name}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default function NavbarClient({ navLinks }: { navLinks: NavLink[] }) {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery] = useState("");

  const handleMouseEnter = (label: string) => setActiveDropdown(label);
  const handleMouseLeave = () => setActiveDropdown(null);

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log("Search query:", searchQuery);
    }
  };

  return (
    <div className="bg-white">
      <header className="bg-[#F9FAFB] border-b border-gray-200">
        <div className="flex flex-wrap items-center justify-center gap-4 px-2 md:px-4 py-3 md:py-10 container mx-auto">
          <div className="flex md:hidden items-center gap-4 text-sm">
            <a href="tel:+977-1-4169802" className="text-primary hover:underline font-semibold">
              +977-1-4169802
            </a>
            <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium truncate">
              <EMAIL>
            </a>
          </div>

          <Link href="/" className="flex-shrink-0 flex items-center">
            <Image
              src="/images/logo.png"
              alt="Mustang Airworks Logo"
              width={180}
              height={60}
              className="h-14 w-auto"
              priority
            />
            <div className="hidden md:block">
              <h1 className="text-3xl font-bold text-primary">Mustang Airworks Pvt. Ltd</h1>
              <p className="text-sm font-medium text-secondary">Airbus Helicopter Spare Parts Supplier</p>
            </div>
          </Link>

          <div className="flex flex-1 items-center justify-end gap-8 min-w-0">
            <div className="hidden md:flex flex-col gap-3 text-right min-w-0">
              <div className="flex items-center gap-4 text-[15px]">
                <a href="tel:+977-1-4169802" className="text-primary hover:underline font-semibold">
                  +977-1-4169802
                </a>
                <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium truncate">
                  <EMAIL>
                </a>
              </div>

              <form onSubmit={handleSearch} className="relative w-[310px] flex-shrink-0" role="search">
                <div className="flex items-center border-b-2 border-gray-600 transition">
                  <Input
                    placeholder="Search "
                    className="flex-1 bg-transparent border-0 rounded-none text-gray-800 font-medium placeholder:text-gray-800 tracking-wide focus:ring-0 focus:outline-none"
                    style={{ boxShadow: "none" }}
                  />
                  <Search className="w-7 h-7 text-gray-600 hover:text-secondary" />
                </div>
              </form>
            </div>

            <div className="flex md:hidden items-center gap-4">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-secondary hover:text-primary transition"
                aria-label="Toggle menu"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>

        <nav className="bg-secondary text-white relative">
          <div className="max-w-7xl mx-auto px-4 hidden lg:flex items-center justify-between h-14">
            <div className="flex space-x-8 font-semibold text-base">
              {navLinks.map((link) => {
                const hasDropdown = Boolean(link.subItems || link.subCategories);
                return (
                  <div
                    key={link.id || link.href}
                    className="relative"
                    onMouseEnter={() => (hasDropdown ? handleMouseEnter(link.label) : null)}
                    onMouseLeave={handleMouseLeave}
                  >
                    <Link href={link.href} className="flex items-center hover:text-primary transition-colors">
                      {link.label}
                      {hasDropdown && <ChevronDown className="ml-1 w-4 h-4" />}
                    </Link>

                    {activeDropdown === link.label && hasDropdown && (
                      <Dropdown
                        items={(link.subCategories ?? link.subItems)!}
                        title={link.label}
                        isCategory={!!link.subCategories}
                        onStayOpen={() => setActiveDropdown(link.label)}
                        onClose={handleMouseLeave}
                      />
                    )}
                  </div>
                );
              })}
            </div>

            <Link
              href="/line-card"
              className="bg-primary hover:bg-red-700 px-4 py-5 rounded text-xs font-semibold transition-colors"
            >
              LINE CARD 2025
            </Link>
          </div>

          {isMenuOpen && (
            <div className="lg:hidden bg-secondary border-t border-primary text-white">
              <div className="px-4 py-4 space-y-4 overflow-y-auto max-h-[calc(100vh-4rem)] overscroll-contain">
                {navLinks.map((link) => (
                  <div key={link.id || link.href}>
                    {link.subCategories || link.subItems ? (
                      <>
                        <button
                          onClick={() =>
                            setActiveDropdown(activeDropdown === link.label ? null : link.label)
                          }
                          className="flex items-center justify-between w-full hover:text-primary font-bold py-2"
                        >
                          {link.label}
                          <ChevronDown
                            className={`w-4 h-4 transform transition-transform ${activeDropdown === link.label ? "rotate-180" : ""
                              }`}
                          />
                        </button>

                        {activeDropdown === link.label && (
                          <div className="pl-4 mt-2 space-y-2 text-white">
                            {link.subCategories
                              ? link.subCategories.map((cat) => (
                                <div key={cat.id}>
                                  <h4 className="font-semibold mb-1">{cat.category}</h4>
                                  {cat.items.map((item) => (
                                    <Link
                                      key={item.id}
                                      href={item.href}
                                      className="block text-xs hover:text-white py-1"
                                      onClick={() => setIsMenuOpen(false)}
                                    >
                                      → {item.name}
                                    </Link>
                                  ))}
                                </div>
                              ))
                              : link.subItems?.map((item) => (
                                <Link
                                  key={item.id}
                                  href={item.href}
                                  className="block text-xs hover:text-white py-1"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  → {item.name}
                                </Link>
                              ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={link.href}
                        className="block hover:text-primary font-bold py-2"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {link.label}
                      </Link>
                    )}
                  </div>
                ))}

                <Link
                  href="/line-card"
                  className="block bg-primary hover:bg-red-700 px-4 py-2 rounded text-center text-sm font-semibold mt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  LINE CARD 2025
                </Link>
              </div>
            </div>
          )}
        </nav>
      </header>
    </div >
  );
}


// 'use client';

// import { useState } from 'react';
// import Link from 'next/link';
// import { ChevronDown, X, Menu, Search } from 'lucide-react';
// import Image from 'next/image';
// import { Input } from '../ui/input';

// interface NavItem {
//   name: string;
//   href: string;
// }

// interface NavCategory {
//   category: string;
//   items: NavItem[];
// }

// type NavLink = {
//   label: string;
//   href: string;
//   subItems?: NavItem[];
//   subCategories?: NavCategory[];
// };

// const navLinks: NavLink[] = [
//   {
//     label: 'HOME',
//     href: '/',
//   },
//   {
//     label: 'AIRCRAFT',
//     href: '/aircraft',
//     subCategories: [
//       {
//         category: 'AIRLINES',
//         items: [
//           { name: 'AIRBUS A330-A340', href: '/aircraft/airbus-a330-a340' },
//           { name: 'AIRBUS A320 FAMILY AND NEO', href: '/aircraft/airbus-a320' },
//           { name: 'BOEING B737NG', href: '/aircraft/boeing-b737ng' },
//           { name: 'BOEING B727', href: '/aircraft/boeing-b727' },
//           { name: 'BOEING B767/B777', href: '/aircraft/boeing-b767-b777' },
//           { name: 'EMBRAER E-JET FAMILY E190 AND E170', href: '/aircraft/embraer-ejet' },
//           { name: 'BAE146/AVRO RJ', href: '/aircraft/bae146-avro' },
//           { name: 'BAE125-700/800 (HAWKER)', href: '/aircraft/bae125-hawker' },
//           { name: 'BAE JETSTREAM 31/32', href: '/aircraft/bae-jetstream' },
//           { name: 'ATR 72', href: '/aircraft/atr-72' },
//           { name: 'BOMBARDIER DASH 8', href: '/aircraft/bombardier-dash8' },
//           { name: 'GULFSTREAM G-IV', href: '/aircraft/gulfstream-g4' },
//           { name: 'BAES HAWK JET', href: '/aircraft/baes-hawk' },
//           { name: 'SAAB 340 / SAAB 2000', href: '/aircraft/saab-340-2000' }
//         ],
//       },
//       {
//         category: 'HELICOPTERS',
//         items: [
//           { name: 'AGUSTA AW109A, AW109C, AW109E AND AW109S', href: '/aircraft/agusta-aw109' },
//           { name: 'AGUSTA AW139', href: '/aircraft/agusta-aw139' },
//           { name: 'AGUSTA AW189', href: '/aircraft/agusta-aw189' },
//           { name: 'AGUSTA AW101 MERLIN', href: '/aircraft/agusta-aw101' },
//           { name: 'AIRBUS HELICOPTERS SUPER PUMA', href: '/aircraft/airbus-super-puma' },
//           { name: 'LYNX HELICOPTERS', href: '/aircraft/lynx-helicopters' },
//           { name: 'SIKORSKY S61/SEAKING', href: '/aircraft/sikorsky-s61' },
//           { name: 'SIKORSKY S76 HELICOPTER SPARES', href: '/aircraft/sikorsky-s76' },
//           { name: 'SIKORSKY S92', href: '/aircraft/sikorsky-s92' },
//           { name: 'AIRBUS HELICOPTERS AS350', href: '/aircraft/airbus-as350' },
//         ],
//       },
//     ],
//   },
//   {
//     label: 'ABOUT US',
//     href: '/about',
//     subItems: [
//       { name: 'OUR COMPANY', href: '/about/company' },
//       { name: 'OUR TEAM', href: '/about/team' },
//       { name: 'OUR MISSION', href: '/about/mission' },
//       { name: 'CAREERS', href: '/about/careers' },
//       { name: 'NEWS & MEDIA', href: '/about/news' },
//     ],
//   },
//   {
//     label: 'TAILORED SYSTEM SUPPORT',
//     href: '/tailored-system-support',
//     subItems: [
//       { name: 'AIRCRAFT COMPONENT REPAIR', href: '/tailored-system-support/repair' },
//       { name: 'ENGINEERING SERVICES', href: '/tailored-system-support/engineering' },
//       { name: 'TECHNICAL SUPPORT', href: '/tailored-system-support/technical' },
//       { name: 'LOGISTICS SOLUTIONS', href: '/tailored-system-support/logistics' },
//       { name: 'INVENTORY MANAGEMENT', href: '/tailored-system-support/inventory' },
//     ],
//   },
//   {
//     label: 'DEFENCE',
//     href: '/defence',
//     subItems: [
//       { name: 'MILITARY AIRCRAFT', href: '/defence/military-aircraft' },
//       { name: 'HELICOPTER SYSTEMS', href: '/defence/helicopter-systems' },
//       { name: 'AVIONICS', href: '/defence/avionics' },
//       { name: 'GROUND SUPPORT EQUIPMENT', href: '/defence/ground-support' },
//       { name: 'TRAINING SOLUTIONS', href: '/defence/training' },
//     ],
//   },
//   {
//     label: 'CERTIFIED',
//     href: '/certified',
//   },
//   {
//     label: 'CONTACT US',
//     href: '/contact',
//   },
// ];

// export default function Navbar() {
//   const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
//   const [isMenuOpen, setIsMenuOpen] = useState(false);

//   const [searchQuery] = useState<string>('');

//   const handleMouseEnter = (label: string) => setActiveDropdown(label);
//   const handleMouseLeave = () => setActiveDropdown(null);

//   const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
//     e.preventDefault();
//     if (searchQuery.trim()) {
//       console.log('Search query:', searchQuery);
//     }
//   };

//   const renderDropdownMenu = (
//     items: NavItem[] | NavCategory[],
//     title: string,
//     isCategory = false
//   ) => (
//     <div
//       className="absolute top-full left-0 bg-secondary shadow-lg z-50 w-[500px] p-6 border-t-2 border-primary"
//       onMouseEnter={() => setActiveDropdown(title)}
//       onMouseLeave={handleMouseLeave}
//     >
//       <h3 className="text-2xl font-semibold mb-6 text-white border-l-4 border-primary pl-3">{title}</h3>
//       {isCategory ? (
//         <div className="grid grid-cols-2 gap-6 text-white">
//           {(items as NavCategory[]).map((category) => (
//             <div key={category.category}>
//               <h4 className="text-lg font-semibold mb-3 flex items-center">
//                 <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
//                 {category.category}
//               </h4>
//               <ul className="space-y-2 text-sm">
//                 {category.items.map((item) => (
//                   <li key={item.name}>
//                     <Link
//                       href={item.href}
//                       className="hover:text-primary cursor-pointer transition-colors block"
//                       onClick={() => setActiveDropdown(null)}
//                     >
//                       → {item.name}
//                     </Link>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           ))}
//         </div>
//       ) : (
//         <ul className="space-y-3 text-white text-sm">
//           {(items as NavItem[]).map((item) => (
//             <li key={item.name}>
//               <Link
//                 href={item.href}
//                 className="hover:text-primary cursor-pointer transition-colors block"
//                 onClick={() => setActiveDropdown(null)}
//               >
//                 → {item.name}
//               </Link>
//             </li>
//           ))}
//         </ul>
//       )}
//     </div>
//   );


//   return (
//     <div className="bg-white">
//       <header className="bg-[#F9FAFB] border-b border-gray-200">

//         <div className="flex flex-wrap items-center justify-center gap-4 px-2 md:px-4 py-3 md:py-10 container mx-auto">
//           <div className="flex md:hidden items-center gap-4 text-sm">
//             <a href="tel:+977-1-4169802" className="text-primary hover:underline font-semibold">
//               +977-1-4169802
//             </a>
//             <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium truncate">
//               <EMAIL>
//             </a>
//           </div>

//           <Link href="/" className="flex-shrink-0 flex items-center">
//             <Image
//               src="/images/logo.png"
//               alt="Mustang Airworks Logo"
//               width={180}
//               height={60}
//               className="h-14 w-auto"
//               priority
//             />
//             <div className='hidden md:block'>
//               <h1 className="text-3xl font-bold text-primary">Mustang Airworks Pvt. Ltd </h1>
//               <p className="text-sm font-medium text-secondary">Airbus Helicopter Spare Parts Supplier</p>
//             </div>
//           </Link>

//           <div className="flex flex-1 items-center justify-end gap-8 min-w-0">
//             <div className="hidden md:flex flex-col gap-3 text-right min-w-0">
//               <div className="flex items-center gap-4 text-[15px]">
//                 <a href="tel:+977-1-4169802" className="text-primary hover:underline font-semibold">
//                   +977-1-4169802
//                 </a>
//                 <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium truncate">
//                   <EMAIL>
//                 </a>
//               </div>

//               <form
//                 onSubmit={handleSearch}
//                 className="relative w-[310px] flex-shrink-0"
//                 role="search"
//               >
//                 <div className="flex items-center border-b-2 border-gray-600 transition">
//                   <Input
//                     placeholder="Search "
//                     className="flex-1 bg-transparent border-0 rounded-none text-gray-800 font-medium placeholder:text-gray-800 tracking-wide focus:ring-0 focus:outline-none"
//                     style={{ boxShadow: 'none' }}
//                   />
//                   <Search className="w-7 h-7 text-gray-600 hover:text-secondary" />
//                 </div>
//               </form>
//             </div>

//             <div className="flex md:hidden items-center gap-4">
//               {/* <form
//                 onSubmit={handleSearch}
//                 className="relative w-[150px] flex-shrink-0"
//                 role="search"
//               >
//                 <div className="flex items-center border-b-2 border-gray-600 transition">
//                   <Input
//                     placeholder="Search"
//                     className="flex-1 bg-transparent border-0 rounded-none text-gray-300 font-medium placeholder:text-gray-400  tracking-wide focus:ring-0 focus:outline-none"
//                     style={{ boxShadow: 'none' }}
//                   />
//                   <Search className="w-7 h-7 text-gray-600 hover:text-secondary" />
//                 </div>
//               </form> */}

//               {/* Mobile Menu Button */}
//               <button
//                 onClick={() => setIsMenuOpen(!isMenuOpen)}
//                 className="text-secondary hover:text-primary transition"
//                 aria-label="Toggle menu"
//               >
//                 {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
//               </button>
//             </div>
//           </div>
//         </div>

//         <nav className="bg-secondary text-white relative">
//           <div className="max-w-7xl mx-auto px-4 hidden lg:flex items-center justify-between h-14">
//             {/* Desktop Links */}
//             <div className="flex space-x-8 font-semibold text-base">
//               {navLinks.map((link) => (
//                 <div
//                   key={link.label}
//                   className="relative "
//                   onMouseEnter={() =>
//                     link.subItems || link.subCategories
//                       ? handleMouseEnter(link.label)
//                       : null
//                   }
//                   onMouseLeave={handleMouseLeave}
//                 >
//                   <Link
//                     href={link.href}
//                     className="flex items-center hover:text-primary transition-colors"
//                   >
//                     {link.label}
//                     {(link.subItems || link.subCategories) && (
//                       <ChevronDown className="ml-1 w-4 h-4" />
//                     )}
//                   </Link>
//                   {activeDropdown === link.label &&
//                     (link.subCategories || link.subItems) &&
//                     renderDropdownMenu(
//                       (link.subCategories || link.subItems)!,
//                       link.label,
//                       !!link.subCategories
//                     )}
//                 </div>
//               ))}
//             </div>

//             {/* Desktop Right CTA */}
//             <Link
//               href="/line-card"
//               className="bg-primary hover:bg-red-700 px-4 py-5 rounded text-xs font-semibold transition-colors"
//             >
//               LINE CARD 2025
//             </Link>
//           </div>

//           {/* Mobile Dropdown Menu */}
//           {isMenuOpen && (
//             <div className="lg:hidden bg-secondary border-t border-primary text-white">
//               <div
//                 className="px-4 py-4 space-y-4 overflow-y-auto max-h-[calc(100vh-4rem)] overscroll-contain"
//               >
//                 {navLinks.map((link) => (
//                   <div key={link.label}>
//                     {link.subItems || link.subCategories ? (
//                       <>
//                         <button
//                           onClick={() =>
//                             setActiveDropdown(
//                               activeDropdown === link.label ? null : link.label
//                             )
//                           }
//                           className="flex items-center justify-between w-full hover:text-primary font-bold py-2"
//                         >
//                           {link.label}
//                           <ChevronDown
//                             className={`w-4 h-4 transform transition-transform ${activeDropdown === link.label ? 'rotate-180' : ''
//                               }`}
//                           />
//                         </button>
//                         {activeDropdown === link.label && (
//                           <div className="pl-4 mt-2 space-y-2 text-white">
//                             {link.subCategories
//                               ? link.subCategories.map((cat) => (
//                                 <div key={cat.category}>
//                                   <h4 className="font-semibold mb-1">
//                                     {cat.category}
//                                   </h4>
//                                   {cat.items.map((item) => (
//                                     <Link
//                                       key={item.name}
//                                       href={item.href}
//                                       className="block text-xs hover:text-white py-1"
//                                       onClick={() => setIsMenuOpen(false)}
//                                     >
//                                       → {item.name}
//                                     </Link>
//                                   ))}
//                                 </div>
//                               ))
//                               : link.subItems?.map((item) => (
//                                 <Link
//                                   key={item.name}
//                                   href={item.href}
//                                   className="block text-xs hover:text-white py-1"
//                                   onClick={() => setIsMenuOpen(false)}
//                                 >
//                                   → {item.name}
//                                 </Link>
//                               ))}
//                           </div>
//                         )}
//                       </>
//                     ) : (
//                       <Link
//                         href={link.href}
//                         className="block hover:text-primary font-bold py-2"
//                         onClick={() => setIsMenuOpen(false)}
//                       >
//                         {link.label}
//                       </Link>
//                     )}
//                   </div>
//                 ))}

//                 {/* Mobile CTA */}
//                 <Link
//                   href="/line-card"
//                   className="block bg-primary hover:bg-red-700 px-4 py-2 rounded text-center text-sm font-semibold mt-4"
//                   onClick={() => setIsMenuOpen(false)}
//                 >
//                   LINE CARD 2025
//                 </Link>
//               </div>
//             </div>
//           )}
//         </nav>
//       </header>
//     </div>
//   );
// }
