export interface ICategory {
    id: string;
    name: string;
    slug: string;
    categories?: ISubCategory[];
    createdAt?: string;
    updatedAt?: string;
}

export interface Category {
    id: string;
    name: string;
    slug: string;
    createdAt?: string;
    updatedAt?: string;
}


export interface ISubCategory {
    id: string;
    name: string;
    slug: string;
    categoryId: string;
    category?: Category[];
    createdAt?: string;
    updatedAt?: string;
}

export interface IProduct {
    id: string;
    name: string;
    slug: string;
    image: string;
    subCategoryId: string;
    createdAt?: string;
    updatedAt?: string;
    subCategory?: ISubCategory;
    tabs?: IProductTab[];
}

export interface IProductTab {
    id: string;
    name: string;
    productId: string;
    createdAt?: string;
    updatedAt?: string;
}
