import Link from "next/link";
import { notFound } from "next/navigation";
import type { Metadata } from "next";

import getCategory from "@/actions/category/get-category";
import getSubCategory from "@/actions/subcategory/get-subcategory";
import getProduct from "@/actions/product/get-product";

import type { ICategory, ISubCategory, IProduct } from "@/types/category";
import { ChevronRight, Plane } from "lucide-react";

export const revalidate = 0;

export async function generateMetadata(
    { params }: { params: { category: string } }
): Promise<Metadata> {
    const { data: categories = [] } = await getCategory();
    const current = categories.find((c: ICategory) => c.slug === params.category);
    const title = current ? `${current.name} | Mustang Airworks` : "Category | Mustang Airworks";
    const description = current
        ? `Explore ${current.name} products and programs.`
        : "Browse our catalog by category.";
    return { title, description };
}

export async function generateStaticParams() {
    const { data: categories = [] } = await getCategory();
    return categories.map((c: ICategory) => ({ category: c.slug }));
}

function cardVisualsFor(sub: ISubCategory) {
    return {
        image: `${sub.image}` ?? "/images/category-default.jpg",
        gradient: "from-black/60 via-black/40 to-black/20",
        Icon: Plane,
        titleColor: "text-white",
    };
}

export default async function CategoryPage(
    { params }: { params: { category: string } }
) {
    const [catRes, subRes, prodRes] = await Promise.all([
        getCategory(),
        getSubCategory(),
        getProduct(),
    ]);

    const categories: ICategory[] = catRes?.data ?? [];
    const subcategories: ISubCategory[] = subRes?.data ?? [];
    const products: IProduct[] = prodRes?.data ?? [];

    const currentCategory = categories.find((c) => c.slug === params.category);
    if (!currentCategory) {
        notFound();
    }

    const subsForCategory = subcategories.filter(
        (s) => s.categoryId === currentCategory!.id
    );

    const productsBySub: Record<string, IProduct[]> = {};
    for (const p of products) {
        if (!p?.subCategoryId) continue;
        (productsBySub[p.subCategoryId] ??= []).push(p);
    }

    const nonEmptySubs = subsForCategory
        .map((sub) => ({
            sub,
            items: (productsBySub[sub.id] ?? []).filter((p) => p.name && p.slug),
        }))
        .filter((g) => g.items.length > 0);

    const totalProducts = nonEmptySubs.reduce((acc, g) => acc + g.items.length, 0);

    return (
        <section className="py-14 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
            <div className="absolute inset-0 opacity-5">
                <div
                    className="absolute top-0 left-0 w-full h-full"
                    style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/svg%3E")`,
                    }}
                />
            </div>

            <div className="container mx-auto px-4 relative z-10">
                <div className="mb-10 text-center">
                    <h1 className="text-3xl md:text-4xl font-bold text-secondary">
                        {currentCategory!.name}
                    </h1>
                    <p className="text-gray-600 mt-2">
                        {totalProducts > 0
                            ? `${totalProducts} product${totalProducts > 1 ? "s" : ""} in this category`
                            : "No products found in this category yet."}
                    </p>
                    <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6 rounded-full" />
                </div>

                <div className="grid gap-12 lg:grid-cols-2 mb-4 max-w-7xl mx-auto">
                    {nonEmptySubs.map(({ sub, items }) => {
                        const { image, gradient, Icon, titleColor } = cardVisualsFor(sub);

                        return (
                            <article
                                key={sub.id}
                                className="group relative overflow-hidden rounded-3xl bg-white shadow-lg transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl"
                            >
                                <div className="relative h-80 overflow-hidden">
                                    <div
                                        className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110"
                                        style={{ backgroundImage: `url(${image})` }}
                                    />
                                    <div className={`absolute inset-0 bg-gradient-to-t ${gradient}`} />
                                    <div className="absolute bottom-6 left-6 flex items-center gap-3">
                                        <div className="w-14 h-14 bg-white/25 backdrop-blur-md rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform">
                                            <Icon className="w-7 h-7 text-primary" />
                                        </div>
                                        <h3 className={`text-3xl font-bold drop-shadow-lg ${titleColor}`}>{sub.name}</h3>
                                    </div>
                                </div>

                                <div className="bg-white p-4">
                                    <ul className="space-y-1">
                                        {items.map((p, index) => (
                                            <li
                                                key={p.id}
                                                style={{ transitionDelay: `${index * 50}ms` }}
                                                className="transition-all"
                                            >
                                                <Link
                                                    href={`/${currentCategory!.slug}/${p.slug}`}
                                                    className="group/item flex items-center justify-between py-2 px-2 rounded-xl hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 transition-all"
                                                >
                                                    <div className="flex items-center">
                                                        <ChevronRight className="w-4 h-4 text-primary mr-3 group-hover/item:translate-x-1 transition-transform" />
                                                        <span className="font-medium text-gray-700 group-hover/item:text-primary transition-colors">
                                                            {p.name}
                                                        </span>
                                                    </div>
                                                    <div className="w-2 h-2 bg-primary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity" />
                                                </Link>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </article>
                        );
                    })}
                </div>
            </div>
        </section>
    );
}


// import Link from "next/link";
// import { notFound } from "next/navigation";
// import type { Metadata } from "next";

// import getCategory from "@/actions/category/get-category";
// import getSubCategory from "@/actions/subcategory/get-subcategory";
// import getProduct from "@/actions/product/get-product";

// import type { ICategory, ISubCategory, IProduct } from "@/types/category";

// export const revalidate = 0;

// export async function generateMetadata(
//   { params }: { params: { category: string } }
// ): Promise<Metadata> {
//   const { data: categories = [] } = await getCategory();
//   const current = categories.find((c: ICategory) => c.slug === params.category);
//   const title = current ? `${current.name} | Mustang Airworks` : "Category | Mustang Airworks";
//   const description = current
//     ? `Explore ${current.name} products and programs.`
//     : "Browse our catalog by category.";
//   return { title, description };
// }

// export async function generateStaticParams() {
//   const { data: categories = [] } = await getCategory();
//   return categories.map((c: ICategory) => ({ category: c.slug }));
// }

// export default async function CategoryPage(
//   { params }: { params: { category: string } }
// ) {
//   const [catRes, subRes, prodRes] = await Promise.all([
//     getCategory(),
//     getSubCategory(),
//     getProduct(),
//   ]);

//   const categories: ICategory[] = catRes?.data ?? [];
//   const subcategories: ISubCategory[] = subRes?.data ?? [];
//   const products: IProduct[] = prodRes?.data ?? [];

//   const currentCategory = categories.find((c) => c.slug === params.category);
//   if (!currentCategory) {
//     notFound();
//   }

//   const subsForCategory = subcategories.filter(
//     (s) => s.categoryId === currentCategory!.id
//   );

//   const productsBySub: Record<string, IProduct[]> = {};
//   for (const p of products) {
//     if (!p?.subCategoryId) continue;
//     (productsBySub[p.subCategoryId] ??= []).push(p);
//   }

//   const nonEmptySubs = subsForCategory
//     .map((sub) => ({
//       sub,
//       items: (productsBySub[sub.id] ?? []).filter((p) => p.name && p.slug),
//     }))
//     .filter((g) => g.items.length > 0);

//   const totalProducts = nonEmptySubs.reduce((acc, g) => acc + g.items.length, 0);

//   return (
//     <div className="container mx-auto px-4 py-8">
//       {/* Header */}
//       <div className="mb-8">
//         <h1 className="text-3xl font-bold text-secondary">
//           {currentCategory!.name}
//         </h1>
//         <p className="text-gray-600">
//           {totalProducts > 0
//             ? `${totalProducts} product${totalProducts > 1 ? "s" : ""} in this category`
//             : "No products found in this category yet."}
//         </p>
//       </div>

//       {nonEmptySubs.length > 0 ? (
//         <div className="space-y-10">
//           {nonEmptySubs.map(({ sub, items }) => (
//             <section key={sub.id}>
//               <h2 className="text-xl font-semibold mb-4 flex items-center">
//                 <span className="w-1.5 h-6 bg-primary mr-2 inline-block rounded" />
//                 {sub.name}
//               </h2>

//               <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
//                 {items.map((p) => (
//                   <Link
//                     key={p.id}
//                     href={`/${currentCategory!.slug}/${p.slug}`}
//                     className="group rounded-lg border border-gray-200 p-4 hover:shadow transition bg-white"
//                   >
//                     <div className="flex items-start justify-between">
//                       <h3 className="text-base font-semibold text-gray-900 group-hover:text-primary">
//                         {p.name}
//                       </h3>
//                     </div>
//                     <p className="mt-2 text-xs text-gray-500">
//                       Subcategory: {sub.name}
//                     </p>
//                   </Link>
//                 ))}
//               </div>
//             </section>
//           ))}
//         </div>
//       ) : (
//         <div className="text-gray-600">Nothing to show here yet.</div>
//       )}
//     </div>
//   );
// }
