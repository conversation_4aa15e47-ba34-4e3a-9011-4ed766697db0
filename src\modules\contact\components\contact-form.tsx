"use client";

import Link from "next/link";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AtSign, Send } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import ContactInformation from "./contact-info";
import ContactMap from "./contact-map";



const formSchema = z.object({
    name: z.string().min(2, {
        message: "Name must be at least 2 characters.",
    }),
    email: z.string().email({
        message: "Please enter a valid email address.",
    }),
    phone: z.string()
        .min(10, { message: "Phone number must be at least 10 digits." })
        .regex(/^[\+]?[0-9\s\-\(\)]+$/, {
            message: "Please enter a valid phone number."
        }),
    subject: z.string().min(5, {
        message: "Subject must be at least 5 characters.",
    }),
    message: z.string().min(10, {
        message: "Message must be at least 10 characters.",
    }),
});

export default function ContactForm() {
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            email: "",
            phone: "",
            subject: "",
            message: "",
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        // Handle form submission here
        console.log(values);
    }

    return (
        <div className="container mx-auto px-4 ">
            <div className="grid gap-8 md:grid-cols-2">
                <Card className="border-0 shadow-lg">
                    <CardHeader className="pb-4">
                        <CardTitle className="text-2xl">Send us a message</CardTitle>
                        <CardDescription>
                            Fill out the form below and we&apos;ll get back to you as soon as
                            possible.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form
                                onSubmit={form.handleSubmit(onSubmit)}
                                className="space-y-6"
                            >
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Full Name</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Your name" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Email</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="<EMAIL>"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="phone"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Phone Number</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="tel"
                                                    placeholder="+977 9846115880"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="subject"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Subject</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Subject of your inquiry"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="message"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Message</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Please provide details about your inquiry..."
                                                    className="min-h-[120px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <Button
                                    type="submit"
                                    className="w-full text-white"
                                >
                                    <span className="mr-2">Send Message</span>
                                    <Send className="h-4 w-4" />
                                </Button>
                            </form>
                        </Form>
                    </CardContent>
                </Card>

                <ContactInformation />
            </div>

            <ContactMap />

            <div className="mt-12 text-center">
                <h2 className="text-2xl font-bold mb-4">
                    Have questions about our products or need support?
                </h2>
                <p className="text-[#444] text-sm font-medium mb-6">
                    Our friendly support team is always here to help you.
                </p>
                <div className="inline-flex items-center justify-center">
                    <AtSign className="h-5 w-5 mr-1 text-secondary" />
                    <Link
                        href="mailto:<EMAIL>"
                        className="text-secondary font-medium hover:underline"
                    >
                        <EMAIL>
                    </Link>
                </div>
            </div>
        </div>
    );
}