'use client';

import Link from 'next/link';

const cardData = [
  {
    title: 'AIRLINES',
    description: 'Commercial aircraft parts and comprehensive airline support solutions',
    imageUrl: '/images/airline.png',
    href: '/aircraft/airlines',
    gradientFrom: 'from-blue-900',
    gradientTo: 'to-blue-700',
    overlayOpacity: 'bg-black/30',
    hoverOverlayOpacity: 'group-hover:bg-black/20',
  },
  {
    title: 'HELICOPTERS',
    description: 'Rotorcraft parts and specialized helicopter maintenance solutions',
    imageUrl: '/images/helicopter.png',
    href: '/aircraft/helicopters',
    gradientFrom: 'from-yellow-600',
    gradientTo: 'to-orange-600',
    overlayOpacity: 'bg-black/30',
    hoverOverlayOpacity: 'group-hover:bg-black/20',
  },
  {
    title: 'DEFENCE',
    description: 'Military aircraft support with security-cleared processes and documentation',
    imageUrl: '/images/defense.png',
    href: '/aircraft/defence',
    gradientFrom: 'from-green-800',
    gradientTo: 'to-gray-800',
    overlayOpacity: 'bg-black/40',
    hoverOverlayOpacity: 'group-hover:bg-black/30',
  },
];

const CapacitySection = () => {
  return (
    <section className="py-14 bg-gray-50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl text-center font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-2">
            OUR CAPACITY
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6 rounded-full mb-6" />
        </div>

        {/* Cards Grid */}
        <div className="grid md:grid-cols-3 gap-10">
          {cardData.map(({ title, description, imageUrl, href, gradientFrom, gradientTo, overlayOpacity, hoverOverlayOpacity }) => (
            <div
              key={title}
              className="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-400"
              aria-label={`${title} capacity card`}
            >
              <div
                className={`relative h-72 bg-gradient-to-br ${gradientFrom} ${gradientTo} rounded-xl transform gpu-animation group-hover:scale-105 transition-transform duration-500 ease-in-out`}
              >
                {/* Background Image with smooth zoom */}
                <div
                  className="absolute inset-0 bg-cover bg-center filter brightness-90 transition-filter duration-500 group-hover:brightness-100"
                  style={{ backgroundImage: `url(${imageUrl})` }}
                  role="img"
                  aria-label={`${title} background image`}
                />
                {/* Overlay */}
                <div
                  className={`absolute inset-0 ${overlayOpacity} ${hoverOverlayOpacity} transition-colors duration-500 rounded-xl`}
                />
                {/* Content */}
                <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-6">
                  <h3 className="text-4xl font-semibold text-white drop-shadow-md mb-4 tracking-wide shadow-black">
                    {title}
                  </h3>
                  <p className="text-white text-sm max-w-xs opacity-95 leading-relaxed drop-shadow-sm">
                    {description}
                  </p>
                </div>
              </div>
              {/* Link overlay */}
              <Link
                href={href}
                className="absolute inset-0"
                aria-label={`Learn more about ${title}`}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CapacitySection;
